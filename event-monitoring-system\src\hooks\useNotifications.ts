import { useState, useEffect } from 'react';
import { SystemAlert } from '../types';

interface NotificationOptions {
  title: string;
  body: string;
  icon?: string;
  tag?: string;
  requireInteraction?: boolean;
}

export const useNotifications = () => {
  const [permission, setPermission] = useState<NotificationPermission>('default');
  const [isSupported, setIsSupported] = useState(false);

  useEffect(() => {
    // Check if notifications are supported
    setIsSupported('Notification' in window);
    
    if ('Notification' in window) {
      setPermission(Notification.permission);
    }
  }, []);

  const requestPermission = async (): Promise<NotificationPermission> => {
    if (!isSupported) return 'denied';
    
    const result = await Notification.requestPermission();
    setPermission(result);
    return result;
  };

  const showNotification = (options: NotificationOptions): Notification | null => {
    if (!isSupported || permission !== 'granted') return null;

    const notification = new Notification(options.title, {
      body: options.body,
      icon: options.icon || '/vite.svg',
      tag: options.tag,
      requireInteraction: options.requireInteraction || false,
    });

    return notification;
  };

  const showAlertNotification = (alert: SystemAlert): Notification | null => {
    const severityEmoji = {
      critical: '🚨',
      error: '❌',
      warning: '⚠️',
      info: 'ℹ️',
    };

    return showNotification({
      title: `${severityEmoji[alert.severity]} ${alert.type.toUpperCase()} Alert`,
      body: alert.message,
      tag: alert.id,
      requireInteraction: alert.severity === 'critical',
    });
  };

  return {
    isSupported,
    permission,
    requestPermission,
    showNotification,
    showAlertNotification,
  };
};
