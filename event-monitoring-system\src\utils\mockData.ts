import { Incident, ResponseUnit, VideoFeed, SystemAlert, EventZone } from '../types';

// Mock data generator for demonstration purposes
export const generateMockIncidents = (): Incident[] => [
  {
    id: 'inc-001',
    type: 'crowd_surge',
    severity: 'critical',
    status: 'responding',
    location: {
      lat: 40.7128,
      lng: -74.0060,
      zone: 'Main Stage',
      description: 'Front barrier area near stage left',
    },
    timestamp: new Date(Date.now() - 300000), // 5 minutes ago
    description: 'AI detected dangerous crowd density levels exceeding 4.5 people per square meter. Potential for crowd surge incident.',
    confidence: 0.94,
    videoFeedId: 'cam-001',
    assignedUnits: ['sec-alpha', 'med-001'],
    aiAnalysis: {
      summary: 'High crowd density with restricted movement patterns detected. Risk of stampede if not addressed immediately.',
      keyFrames: ['frame_001.jpg', 'frame_002.jpg'],
      detectionDetails: {
        crowdDensity: 4.7,
        movementPattern: 'restricted',
        exitAccessibility: 'limited',
      },
    },
  },
  {
    id: 'inc-002',
    type: 'medical',
    severity: 'high',
    status: 'verified',
    location: {
      lat: 40.7130,
      lng: -74.0058,
      zone: 'Food Court',
      description: 'Near vendor booth 12, east side',
    },
    timestamp: new Date(Date.now() - 600000), // 10 minutes ago
    description: 'Person collapsed, appears unconscious. Bystanders providing assistance.',
    confidence: 0.89,
    videoFeedId: 'cam-003',
    assignedUnits: ['med-002'],
    humanVerification: {
      verified: true,
      verifiedBy: 'operator-sarah',
      verificationTime: new Date(Date.now() - 580000),
      notes: 'Confirmed medical emergency. Ambulance dispatched, ETA 4 minutes.',
    },
  },
  {
    id: 'inc-003',
    type: 'fire',
    severity: 'medium',
    status: 'resolved',
    location: {
      lat: 40.7125,
      lng: -74.0065,
      zone: 'Backstage',
      description: 'Equipment storage area',
    },
    timestamp: new Date(Date.now() - 1800000), // 30 minutes ago
    description: 'Smoke detected from electrical equipment. Fire suppression system activated.',
    confidence: 0.82,
    videoFeedId: 'cam-005',
    assignedUnits: ['fire-001'],
  },
];

export const generateMockResponseUnits = (): ResponseUnit[] => [
  {
    id: 'sec-alpha',
    type: 'security',
    name: 'Security Team Alpha',
    status: 'dispatched',
    location: { lat: 40.7126, lng: -74.0061 },
    assignedIncident: 'inc-001',
    estimatedArrival: new Date(Date.now() + 120000), // 2 minutes
    route: [
      { lat: 40.7120, lng: -74.0070 },
      { lat: 40.7125, lng: -74.0065 },
      { lat: 40.7128, lng: -74.0060 },
    ],
  },
  {
    id: 'med-001',
    type: 'medical',
    name: 'Medical Unit 1',
    status: 'dispatched',
    location: { lat: 40.7129, lng: -74.0059 },
    assignedIncident: 'inc-001',
    estimatedArrival: new Date(Date.now() + 180000), // 3 minutes
  },
  {
    id: 'med-002',
    type: 'medical',
    name: 'Medical Unit 2',
    status: 'busy',
    location: { lat: 40.7130, lng: -74.0058 },
    assignedIncident: 'inc-002',
  },
  {
    id: 'sec-beta',
    type: 'security',
    name: 'Security Team Beta',
    status: 'available',
    location: { lat: 40.7135, lng: -74.0055 },
  },
  {
    id: 'fire-001',
    type: 'fire',
    name: 'Fire Safety Team',
    status: 'available',
    location: { lat: 40.7115, lng: -74.0075 },
  },
  {
    id: 'police-001',
    type: 'police',
    name: 'Police Unit 1',
    status: 'available',
    location: { lat: 40.7140, lng: -74.0050 },
  },
];

export const generateMockVideoFeeds = (): VideoFeed[] => [
  {
    id: 'cam-001',
    name: 'Main Stage Camera 1',
    url: 'rtmp://stream.example.com/live/cam001',
    location: { lat: 40.7128, lng: -74.0060, zone: 'Main Stage' },
    status: 'active',
    lastUpdate: new Date(),
  },
  {
    id: 'cam-002',
    name: 'Main Stage Camera 2',
    url: 'rtmp://stream.example.com/live/cam002',
    location: { lat: 40.7127, lng: -74.0061, zone: 'Main Stage' },
    status: 'active',
    lastUpdate: new Date(),
  },
  {
    id: 'cam-003',
    name: 'Food Court Overview',
    url: 'rtmp://stream.example.com/live/cam003',
    location: { lat: 40.7130, lng: -74.0058, zone: 'Food Court' },
    status: 'active',
    lastUpdate: new Date(),
  },
  {
    id: 'cam-004',
    name: 'Entrance Gate A',
    url: 'rtmp://stream.example.com/live/cam004',
    location: { lat: 40.7135, lng: -74.0055, zone: 'Entrance' },
    status: 'active',
    lastUpdate: new Date(),
  },
  {
    id: 'cam-005',
    name: 'Backstage Security',
    url: 'rtmp://stream.example.com/live/cam005',
    location: { lat: 40.7125, lng: -74.0065, zone: 'Backstage' },
    status: 'inactive',
    lastUpdate: new Date(Date.now() - 300000),
  },
  {
    id: 'cam-006',
    name: 'Emergency Exit B',
    url: 'rtmp://stream.example.com/live/cam006',
    location: { lat: 40.7132, lng: -74.0052, zone: 'Emergency Exit' },
    status: 'error',
    lastUpdate: new Date(Date.now() - 600000),
  },
  {
    id: 'cam-007',
    name: 'VIP Area',
    url: 'rtmp://stream.example.com/live/cam007',
    location: { lat: 40.7133, lng: -74.0057, zone: 'VIP Area' },
    status: 'active',
    lastUpdate: new Date(),
  },
  {
    id: 'cam-008',
    name: 'Parking Lot',
    url: 'rtmp://stream.example.com/live/cam008',
    location: { lat: 40.7120, lng: -74.0070, zone: 'Parking' },
    status: 'active',
    lastUpdate: new Date(),
  },
];

export const generateMockAlerts = (): SystemAlert[] => [
  {
    id: 'alert-001',
    type: 'incident',
    severity: 'critical',
    message: 'Critical crowd surge detected at Main Stage - immediate response required',
    timestamp: new Date(Date.now() - 60000),
    acknowledged: false,
  },
  {
    id: 'alert-002',
    type: 'system',
    severity: 'warning',
    message: 'Camera feed cam-006 (Emergency Exit B) has been offline for 10 minutes',
    timestamp: new Date(Date.now() - 600000),
    acknowledged: false,
  },
  {
    id: 'alert-003',
    type: 'unit',
    severity: 'info',
    message: 'Medical Unit 2 has arrived at Food Court incident location',
    timestamp: new Date(Date.now() - 300000),
    acknowledged: true,
    acknowledgedBy: 'operator-john',
  },
];

export const generateMockEventZones = (): EventZone[] => [
  {
    id: 'zone-001',
    name: 'Main Stage',
    type: 'stage',
    coordinates: [
      { lat: 40.7126, lng: -74.0062 },
      { lat: 40.7130, lng: -74.0062 },
      { lat: 40.7130, lng: -74.0058 },
      { lat: 40.7126, lng: -74.0058 },
    ],
    capacity: 5000,
    currentOccupancy: 4200,
    status: 'critical',
    videoFeeds: ['cam-001', 'cam-002'],
  },
  {
    id: 'zone-002',
    name: 'Food Court',
    type: 'food',
    coordinates: [
      { lat: 40.7128, lng: -74.0060 },
      { lat: 40.7132, lng: -74.0060 },
      { lat: 40.7132, lng: -74.0056 },
      { lat: 40.7128, lng: -74.0056 },
    ],
    capacity: 1500,
    currentOccupancy: 1200,
    status: 'crowded',
    videoFeeds: ['cam-003'],
  },
  {
    id: 'zone-003',
    name: 'Entrance Gate A',
    type: 'entrance',
    coordinates: [
      { lat: 40.7133, lng: -74.0057 },
      { lat: 40.7137, lng: -74.0057 },
      { lat: 40.7137, lng: -74.0053 },
      { lat: 40.7133, lng: -74.0053 },
    ],
    capacity: 800,
    currentOccupancy: 150,
    status: 'normal',
    videoFeeds: ['cam-004'],
  },
  {
    id: 'zone-004',
    name: 'VIP Area',
    type: 'stage',
    coordinates: [
      { lat: 40.7131, lng: -74.0059 },
      { lat: 40.7135, lng: -74.0059 },
      { lat: 40.7135, lng: -74.0055 },
      { lat: 40.7131, lng: -74.0055 },
    ],
    capacity: 200,
    currentOccupancy: 180,
    status: 'normal',
    videoFeeds: ['cam-007'],
  },
];

// Utility function to simulate real-time data updates
export const simulateDataUpdate = () => {
  const incidents = generateMockIncidents();
  const units = generateMockResponseUnits();
  const feeds = generateMockVideoFeeds();
  const alerts = generateMockAlerts();
  const zones = generateMockEventZones();

  return {
    incidents,
    responseUnits: units,
    videoFeeds: feeds,
    alerts,
    eventZones: zones,
  };
};
