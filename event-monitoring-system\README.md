# AI-Powered Event Monitoring System

A comprehensive real-time monitoring and safety management platform for large-scale public events such as music festivals, political rallies, and sporting events.

## 🚀 Features

### Core Capabilities
- **Real-time Video Analysis**: Live processing of multiple video feeds with AI-powered incident detection
- **Intelligent Incident Detection**: Automated detection of crowd surges, medical emergencies, fire/smoke, and security threats
- **Smart Response Coordination**: Automated dispatch of response units with optimal routing
- **Natural Language Interface**: AI assistant for querying system status and generating reports
- **Interactive Event Map**: Real-time visualization of incidents, response units, and event zones
- **Human-in-the-Loop Verification**: Safety mechanisms for critical decisions with confidence thresholds

### Technical Features
- **Modern React Frontend**: Built with TypeScript, Material-UI, and responsive design
- **Real-time Communication**: WebSocket integration for live updates and notifications
- **Interactive Maps**: Leaflet integration for geospatial visualization
- **Comprehensive Dashboard**: Multi-view interface with customizable layouts
- **Browser Notifications**: Real-time alerts for critical incidents
- **Mock Data Simulation**: Realistic data for demonstration and testing

## 🏗️ Architecture

### Frontend Stack
- **React 18** with TypeScript
- **Material-UI (MUI)** for component library
- **React Leaflet** for interactive maps
- **Socket.IO Client** for real-time communication
- **Recharts** for data visualization
- **React Hook Form** with Yup validation

### Key Components
- **Dashboard**: Main control interface with multiple views
- **Video Feed Grid**: Live video monitoring with AI overlays
- **Incident Management**: Detection, classification, and response coordination
- **Interactive Map**: Real-time geospatial visualization
- **AI Chat Interface**: Natural language query system
- **Notification Center**: Real-time alert management

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ 
- npm or yarn package manager

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd event-monitoring-system
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to `http://localhost:5173`

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## 🎯 Usage

### Main Dashboard
The dashboard provides multiple views:
- **Overview**: System statistics and key metrics
- **Video Feeds**: Live camera monitoring
- **Incidents**: Active incident management
- **Event Map**: Geospatial visualization
- **AI Assistant**: Natural language queries

### Key Features

#### 1. Incident Detection
- Automatic detection of various incident types
- Confidence scoring and human verification
- Real-time status updates and response tracking

#### 2. Video Monitoring
- Multiple camera feeds with status indicators
- Fullscreen viewing capabilities
- Zone-based filtering and organization

#### 3. Response Coordination
- Real-time unit tracking and dispatch
- Optimal routing visualization
- Status monitoring and ETA calculations

#### 4. AI Assistant
- Natural language query interface
- Contextual responses about system status
- Report generation and analysis

#### 5. Real-time Notifications
- Browser notifications for critical alerts
- Acknowledgment system for alert management
- Severity-based prioritization

## 🔧 Configuration

### Environment Variables
Create a `.env` file in the root directory:

```env
REACT_APP_SOCKET_URL=ws://localhost:3001
REACT_APP_API_BASE_URL=http://localhost:3000
```

### Mock Data
The system includes comprehensive mock data for demonstration:
- Sample incidents with various severity levels
- Response units with different types and statuses
- Video feeds with realistic status simulation
- Event zones with capacity monitoring

## 🎨 Design Principles

### Safety-First Design
- High contrast colors for critical alerts
- Clear visual hierarchy for urgent information
- Redundant notification systems
- Accessibility considerations for control room environments

### Real-time Performance
- Optimized rendering for live data updates
- Efficient state management with React Context
- Minimal latency for critical notifications
- Responsive design for various screen sizes

### User Experience
- Intuitive navigation with clear visual cues
- Contextual information and tooltips
- Keyboard shortcuts for common actions
- Mobile-responsive design

## 🔮 Future Enhancements

### Backend Integration
- Node.js/Express API server
- Real-time video processing pipeline
- Database integration for historical data
- Authentication and authorization system

### AI Capabilities
- Advanced computer vision models
- Predictive analytics for crowd behavior
- Natural language processing improvements
- Machine learning model training interface

### Advanced Features
- Multi-event management
- Historical data analysis and reporting
- Integration with external emergency services
- Mobile application for field personnel

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Material-UI team for the excellent component library
- Leaflet community for mapping capabilities
- React team for the robust framework
- Open source community for various dependencies

---

**Note**: This is a demonstration system with mock data. For production use, integrate with actual video feeds, AI processing services, and emergency response systems.
