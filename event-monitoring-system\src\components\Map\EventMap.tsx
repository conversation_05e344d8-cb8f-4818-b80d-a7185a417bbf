import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  ToggleButton,
  ToggleButtonGroup,
  Chip,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Warning as WarningIcon,
  VideoCall as VideoIcon,
  Security as SecurityIcon,
  LocalFireDepartment as FireIcon,
  LocalHospital as MedicalIcon,
  ZoomIn as ZoomInIcon,
  ZoomOut as ZoomOutIcon,
  MyLocation as CenterIcon,
} from '@mui/icons-material';
import { MapContainer, TileLayer, Marker, Popup, Circle, Polyline } from 'react-leaflet';
import { LatLngExpression, Icon } from 'leaflet';
import { useSocket } from '../../contexts/SocketContext';
import { Incident, ResponseUnit, VideoFeed } from '../../types';
import 'leaflet/dist/leaflet.css';

// Fix for default markers in react-leaflet
delete (Icon.Default.prototype as any)._getIconUrl;
Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

interface MapLayerControlProps {
  layers: string[];
  activeLayers: string[];
  onLayerToggle: (layer: string) => void;
}

const MapLayerControl: React.FC<MapLayerControlProps> = ({ layers, activeLayers, onLayerToggle }) => {
  return (
    <Paper sx={{ position: 'absolute', top: 10, right: 10, zIndex: 1000, p: 1 }}>
      <Typography variant="caption" display="block" sx={{ mb: 1 }}>
        Map Layers
      </Typography>
      <ToggleButtonGroup
        orientation="vertical"
        size="small"
        value={activeLayers}
        onChange={(_, newLayers) => {
          // Handle toggle for each layer
          layers.forEach(layer => {
            const wasActive = activeLayers.includes(layer);
            const isActive = newLayers.includes(layer);
            if (wasActive !== isActive) {
              onLayerToggle(layer);
            }
          });
        }}
      >
        <ToggleButton value="incidents">
          <WarningIcon sx={{ mr: 1 }} />
          Incidents
        </ToggleButton>
        <ToggleButton value="cameras">
          <VideoIcon sx={{ mr: 1 }} />
          Cameras
        </ToggleButton>
        <ToggleButton value="units">
          <SecurityIcon sx={{ mr: 1 }} />
          Units
        </ToggleButton>
        <ToggleButton value="zones">
          Zones
        </ToggleButton>
      </ToggleButtonGroup>
    </Paper>
  );
};

const EventMap: React.FC = () => {
  const { incidents, responseUnits, videoFeeds } = useSocket();
  const [activeLayers, setActiveLayers] = useState<string[]>(['incidents', 'cameras', 'units', 'zones']);
  const [mapCenter] = useState<LatLngExpression>([40.7128, -74.0060]); // NYC coordinates
  const [mapZoom, setMapZoom] = useState(16);

  // Mock data for demonstration
  const mockIncidents: Incident[] = [
    {
      id: '1',
      type: 'crowd_surge',
      severity: 'critical',
      status: 'responding',
      location: { lat: 40.7128, lng: -74.0060, zone: 'Main Stage', description: 'Front barrier' },
      timestamp: new Date(),
      description: 'Crowd surge detected',
      confidence: 0.92,
      videoFeedId: '1',
      assignedUnits: ['unit-1'],
    },
    {
      id: '2',
      type: 'medical',
      severity: 'high',
      status: 'verified',
      location: { lat: 40.7130, lng: -74.0058, zone: 'Food Court', description: 'Vendor area' },
      timestamp: new Date(),
      description: 'Medical emergency',
      confidence: 0.87,
      videoFeedId: '2',
      assignedUnits: ['medical-1'],
    },
  ];

  const mockUnits: ResponseUnit[] = [
    {
      id: 'unit-1',
      type: 'security',
      name: 'Security Team Alpha',
      status: 'dispatched',
      location: { lat: 40.7125, lng: -74.0062 },
      assignedIncident: '1',
    },
    {
      id: 'medical-1',
      type: 'medical',
      name: 'Medical Unit 1',
      status: 'dispatched',
      location: { lat: 40.7132, lng: -74.0056 },
      assignedIncident: '2',
    },
    {
      id: 'unit-2',
      type: 'security',
      name: 'Security Team Beta',
      status: 'available',
      location: { lat: 40.7120, lng: -74.0070 },
    },
  ];

  const mockCameras: VideoFeed[] = [
    {
      id: '1',
      name: 'Main Stage Camera',
      url: '',
      location: { lat: 40.7128, lng: -74.0060, zone: 'Main Stage' },
      status: 'active',
      lastUpdate: new Date(),
    },
    {
      id: '2',
      name: 'Food Court Camera',
      url: '',
      location: { lat: 40.7130, lng: -74.0058, zone: 'Food Court' },
      status: 'active',
      lastUpdate: new Date(),
    },
    {
      id: '3',
      name: 'Entrance Camera',
      url: '',
      location: { lat: 40.7135, lng: -74.0055, zone: 'Entrance' },
      status: 'inactive',
      lastUpdate: new Date(),
    },
  ];

  const allIncidents = incidents.length > 0 ? incidents : mockIncidents;
  const allUnits = responseUnits.length > 0 ? responseUnits : mockUnits;
  const allCameras = videoFeeds.length > 0 ? videoFeeds : mockCameras;

  const handleLayerToggle = (layer: string) => {
    setActiveLayers(prev => 
      prev.includes(layer) 
        ? prev.filter(l => l !== layer)
        : [...prev, layer]
    );
  };

  const getIncidentIcon = (incident: Incident) => {
    const color = incident.severity === 'critical' ? 'red' : 
                  incident.severity === 'high' ? 'orange' : 
                  incident.severity === 'medium' ? 'yellow' : 'green';
    
    return new Icon({
      iconUrl: `data:image/svg+xml;base64,${btoa(`
        <svg width="25" height="25" viewBox="0 0 25 25" xmlns="http://www.w3.org/2000/svg">
          <circle cx="12.5" cy="12.5" r="10" fill="${color}" stroke="white" stroke-width="2"/>
          <text x="12.5" y="17" text-anchor="middle" fill="white" font-size="12">!</text>
        </svg>
      `)}`,
      iconSize: [25, 25],
      iconAnchor: [12.5, 12.5],
    });
  };

  const getUnitIcon = (unit: ResponseUnit) => {
    const color = unit.status === 'available' ? 'green' :
                  unit.status === 'dispatched' ? 'blue' :
                  unit.status === 'busy' ? 'orange' : 'gray';
    
    return new Icon({
      iconUrl: `data:image/svg+xml;base64,${btoa(`
        <svg width="25" height="25" viewBox="0 0 25 25" xmlns="http://www.w3.org/2000/svg">
          <rect x="2.5" y="2.5" width="20" height="20" fill="${color}" stroke="white" stroke-width="2"/>
          <text x="12.5" y="17" text-anchor="middle" fill="white" font-size="10">U</text>
        </svg>
      `)}`,
      iconSize: [25, 25],
      iconAnchor: [12.5, 12.5],
    });
  };

  const getCameraIcon = (camera: VideoFeed) => {
    const color = camera.status === 'active' ? 'blue' :
                  camera.status === 'inactive' ? 'gray' : 'red';
    
    return new Icon({
      iconUrl: `data:image/svg+xml;base64,${btoa(`
        <svg width="25" height="25" viewBox="0 0 25 25" xmlns="http://www.w3.org/2000/svg">
          <polygon points="12.5,2.5 22.5,7.5 22.5,17.5 12.5,22.5 2.5,17.5 2.5,7.5" fill="${color}" stroke="white" stroke-width="2"/>
          <text x="12.5" y="17" text-anchor="middle" fill="white" font-size="10">C</text>
        </svg>
      `)}`,
      iconSize: [25, 25],
      iconAnchor: [12.5, 12.5],
    });
  };

  // Event zones (mock data)
  const eventZones = [
    { center: [40.7128, -74.0060] as LatLngExpression, radius: 50, name: 'Main Stage', color: 'blue' },
    { center: [40.7130, -74.0058] as LatLngExpression, radius: 30, name: 'Food Court', color: 'green' },
    { center: [40.7135, -74.0055] as LatLngExpression, radius: 25, name: 'Entrance', color: 'orange' },
  ];

  return (
    <Box sx={{ position: 'relative', height: '100%', minHeight: '500px' }}>
      <Typography variant="h6" gutterBottom>
        Event Map - Real-time Overview
      </Typography>
      
      <Paper sx={{ height: 'calc(100% - 40px)', position: 'relative' }}>
        <MapContainer
          center={mapCenter}
          zoom={mapZoom}
          style={{ height: '100%', width: '100%' }}
          zoomControl={false}
        >
          <TileLayer
            attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          />
          
          {/* Incidents Layer */}
          {activeLayers.includes('incidents') && allIncidents.map(incident => (
            <Marker
              key={incident.id}
              position={[incident.location.lat, incident.location.lng]}
              icon={getIncidentIcon(incident)}
            >
              <Popup>
                <Box>
                  <Typography variant="subtitle2" gutterBottom>
                    {incident.type.replace('_', ' ').toUpperCase()} - {incident.severity.toUpperCase()}
                  </Typography>
                  <Typography variant="body2" gutterBottom>
                    {incident.description}
                  </Typography>
                  <Typography variant="caption" display="block">
                    Location: {incident.location.zone}
                  </Typography>
                  <Typography variant="caption" display="block">
                    Status: {incident.status}
                  </Typography>
                  <Typography variant="caption" display="block">
                    Time: {incident.timestamp.toLocaleTimeString()}
                  </Typography>
                  <Box sx={{ mt: 1 }}>
                    <Chip
                      label={incident.severity}
                      color={incident.severity === 'critical' ? 'error' : 'warning'}
                      size="small"
                    />
                  </Box>
                </Box>
              </Popup>
            </Marker>
          ))}
          
          {/* Response Units Layer */}
          {activeLayers.includes('units') && allUnits.map(unit => (
            <Marker
              key={unit.id}
              position={[unit.location.lat, unit.location.lng]}
              icon={getUnitIcon(unit)}
            >
              <Popup>
                <Box>
                  <Typography variant="subtitle2" gutterBottom>
                    {unit.name}
                  </Typography>
                  <Typography variant="body2" gutterBottom>
                    Type: {unit.type.toUpperCase()}
                  </Typography>
                  <Typography variant="caption" display="block">
                    Status: {unit.status}
                  </Typography>
                  {unit.assignedIncident && (
                    <Typography variant="caption" display="block">
                      Assigned to: Incident {unit.assignedIncident}
                    </Typography>
                  )}
                  <Box sx={{ mt: 1 }}>
                    <Chip
                      label={unit.status}
                      color={unit.status === 'available' ? 'success' : 'primary'}
                      size="small"
                    />
                  </Box>
                </Box>
              </Popup>
            </Marker>
          ))}
          
          {/* Cameras Layer */}
          {activeLayers.includes('cameras') && allCameras.map(camera => (
            <Marker
              key={camera.id}
              position={[camera.location.lat, camera.location.lng]}
              icon={getCameraIcon(camera)}
            >
              <Popup>
                <Box>
                  <Typography variant="subtitle2" gutterBottom>
                    {camera.name}
                  </Typography>
                  <Typography variant="caption" display="block">
                    Zone: {camera.location.zone}
                  </Typography>
                  <Typography variant="caption" display="block">
                    Status: {camera.status}
                  </Typography>
                  <Typography variant="caption" display="block">
                    Last Update: {camera.lastUpdate.toLocaleTimeString()}
                  </Typography>
                  <Box sx={{ mt: 1 }}>
                    <Chip
                      label={camera.status}
                      color={camera.status === 'active' ? 'success' : 'error'}
                      size="small"
                    />
                  </Box>
                </Box>
              </Popup>
            </Marker>
          ))}
          
          {/* Event Zones Layer */}
          {activeLayers.includes('zones') && eventZones.map((zone, index) => (
            <Circle
              key={index}
              center={zone.center}
              radius={zone.radius}
              pathOptions={{
                color: zone.color,
                fillColor: zone.color,
                fillOpacity: 0.1,
                weight: 2,
              }}
            >
              <Popup>
                <Typography variant="subtitle2">
                  {zone.name}
                </Typography>
              </Popup>
            </Circle>
          ))}
          
          {/* Routes for dispatched units */}
          {activeLayers.includes('units') && allUnits
            .filter(unit => unit.status === 'dispatched' && unit.route)
            .map(unit => (
              <Polyline
                key={`route-${unit.id}`}
                positions={unit.route!}
                pathOptions={{ color: 'blue', weight: 3, opacity: 0.7 }}
              />
            ))}
        </MapContainer>
        
        {/* Layer Control */}
        <MapLayerControl
          layers={['incidents', 'cameras', 'units', 'zones']}
          activeLayers={activeLayers}
          onLayerToggle={handleLayerToggle}
        />
        
        {/* Map Controls */}
        <Box sx={{ position: 'absolute', bottom: 10, right: 10, zIndex: 1000 }}>
          <Paper sx={{ p: 1 }}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              <Tooltip title="Zoom In">
                <IconButton size="small" onClick={() => setMapZoom(prev => Math.min(prev + 1, 20))}>
                  <ZoomInIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title="Zoom Out">
                <IconButton size="small" onClick={() => setMapZoom(prev => Math.max(prev - 1, 1))}>
                  <ZoomOutIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title="Center Map">
                <IconButton size="small">
                  <CenterIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </Paper>
        </Box>
      </Paper>
    </Box>
  );
};

export default EventMap;
