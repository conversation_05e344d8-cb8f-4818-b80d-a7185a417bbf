import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  TextField,
  IconButton,
  Avatar,
  Chip,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  CircularProgress,
} from '@mui/material';
import {
  Send as SendIcon,
  SmartToy as AIIcon,
  Person as PersonIcon,
  Warning as WarningIcon,
  VideoCall as VideoIcon,
  Map as MapIcon,
  Assessment as ReportIcon,
} from '@mui/icons-material';
import { ChatMessage } from '../../types';

interface MessageBubbleProps {
  message: ChatMessage;
}

const MessageBubble: React.FC<MessageBubbleProps> = ({ message }) => {
  const isAI = message.type === 'ai';
  
  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: isAI ? 'flex-start' : 'flex-end',
        mb: 2,
      }}
    >
      <Box
        sx={{
          display: 'flex',
          alignItems: 'flex-start',
          maxWidth: '70%',
          flexDirection: isAI ? 'row' : 'row-reverse',
        }}
      >
        <Avatar
          sx={{
            bgcolor: isAI ? 'primary.main' : 'secondary.main',
            width: 32,
            height: 32,
            mx: 1,
          }}
        >
          {isAI ? <AIIcon sx={{ fontSize: 18 }} /> : <PersonIcon sx={{ fontSize: 18 }} />}
        </Avatar>
        
        <Paper
          sx={{
            p: 2,
            backgroundColor: isAI ? 'background.paper' : 'primary.main',
            color: isAI ? 'text.primary' : 'primary.contrastText',
          }}
        >
          <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap' }}>
            {message.content}
          </Typography>
          
          {message.attachments && message.attachments.length > 0 && (
            <Box sx={{ mt: 1 }}>
              {message.attachments.map((attachment, index) => (
                <Chip
                  key={index}
                  label={attachment.name}
                  size="small"
                  icon={
                    attachment.type === 'image' ? <VideoIcon /> :
                    attachment.type === 'video' ? <VideoIcon /> :
                    <ReportIcon />
                  }
                  sx={{ mr: 1, mb: 1 }}
                />
              ))}
            </Box>
          )}
          
          <Typography variant="caption" display="block" sx={{ mt: 1, opacity: 0.7 }}>
            {message.timestamp.toLocaleTimeString()}
          </Typography>
        </Paper>
      </Box>
    </Box>
  );
};

const ChatInterface: React.FC = () => {
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: '1',
      type: 'ai',
      content: 'Hello! I\'m your AI assistant for event monitoring. I can help you with:\n\n• Analyzing incidents and providing summaries\n• Answering questions about current events\n• Providing recommendations for response actions\n• Generating reports\n\nWhat would you like to know?',
      timestamp: new Date(Date.now() - 60000),
    },
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    // Simulate AI response
    setTimeout(() => {
      const aiResponse = generateAIResponse(inputValue);
      const aiMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: aiResponse,
        timestamp: new Date(),
      };
      
      setMessages(prev => [...prev, aiMessage]);
      setIsLoading(false);
    }, 1500);
  };

  const generateAIResponse = (userInput: string): string => {
    const input = userInput.toLowerCase();
    
    if (input.includes('incident') || input.includes('emergency')) {
      return 'Based on current data, I can see 2 active incidents:\n\n1. **Crowd Surge at Main Stage** (Critical)\n   - Status: Response units dispatched\n   - Confidence: 92%\n   - Recommendation: Continue monitoring crowd density\n\n2. **Medical Emergency at Food Court** (High)\n   - Status: Medical unit on scene\n   - Verified by human operator\n   - ETA for ambulance: 3 minutes\n\nWould you like more details about any specific incident?';
    }
    
    if (input.includes('camera') || input.includes('video')) {
      return 'Current video feed status:\n\n✅ **6 cameras online** (85% operational)\n❌ **1 camera offline** (Food Court Camera 3)\n\n**Active Feeds:**\n• Main Stage Camera 1 & 2\n• Entrance Gates A, B, C\n• Emergency Exit Camera\n\n**AI Detection Active:** Crowd analysis, fire/smoke detection, medical emergencies\n\nWould you like me to focus analysis on a specific area?';
    }
    
    if (input.includes('unit') || input.includes('response')) {
      return 'Response unit status:\n\n🟢 **Available Units (3):**\n• Security Team Charlie\n• Medical Unit 2\n• Fire Safety Team\n\n🔵 **Dispatched Units (2):**\n• Security Team Alpha → Main Stage incident\n• Medical Unit 1 → Food Court emergency\n\n**Average Response Time:** 2.3 minutes\n**Coverage:** All zones have assigned units within 5-minute response radius';
    }
    
    if (input.includes('zone') || input.includes('area')) {
      return 'Event zone analysis:\n\n🟢 **Normal (8 zones):** Entrance, VIP, Parking, Restrooms, Vendor Area North, Stage Left, Stage Right, Emergency Assembly\n\n🟡 **Crowded (2 zones):** Main Stage Front, Food Court\n\n🔴 **Critical (1 zone):** Main Stage Barrier Area\n   - Crowd density: 4.2 people/m² (exceeds 4.0 limit)\n   - Recommendation: Implement crowd control measures\n\n**Total Capacity:** 15,000 people\n**Current Occupancy:** 12,800 people (85%)';
    }
    
    if (input.includes('report') || input.includes('summary')) {
      return 'Event Summary Report - ' + new Date().toLocaleDateString() + '\n\n**Overall Status:** ⚠️ Elevated Alert\n\n**Key Metrics:**\n• Total Incidents: 3 (2 active, 1 resolved)\n• Response Time Avg: 2.3 minutes\n• System Uptime: 99.2%\n• Crowd Density: 85% capacity\n\n**Critical Actions Taken:**\n1. Dispatched security to crowd surge\n2. Medical response to food court\n3. Increased monitoring at main stage\n\n**Recommendations:**\n• Continue enhanced monitoring\n• Consider crowd flow management\n• Maintain current response posture\n\nWould you like a detailed incident breakdown?';
    }
    
    if (input.includes('help') || input.includes('what can you do')) {
      return 'I can assist you with:\n\n🔍 **Incident Analysis**\n• Real-time incident summaries\n• Severity assessments\n• Response recommendations\n\n📊 **System Status**\n• Camera feed monitoring\n• Response unit tracking\n• Zone capacity analysis\n\n📋 **Reporting**\n• Generate incident reports\n• System performance summaries\n• Trend analysis\n\n💬 **Natural Language Queries**\nJust ask me questions like:\n• "What happened in the East Zone?"\n• "Show me camera status"\n• "How many units are available?"\n\nWhat specific information do you need?';
    }
    
    // Default response
    return 'I understand you\'re asking about "' + userInput + '". Let me analyze the current situation...\n\nBased on real-time data, I can provide information about:\n• Current incidents and their status\n• Video feed monitoring\n• Response unit deployment\n• Zone capacity and crowd flow\n• System health and performance\n\nCould you be more specific about what aspect you\'d like me to focus on? For example:\n• "What incidents are currently active?"\n• "Show me the status of all cameras"\n• "Which response units are available?"';
  };

  const quickQuestions = [
    'What incidents are currently active?',
    'Show me camera status',
    'How many response units are available?',
    'What happened in the East Zone?',
    'Generate incident report',
  ];

  const handleQuickQuestion = (question: string) => {
    setInputValue(question);
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <Typography variant="h6" gutterBottom>
        AI Assistant - Natural Language Queries
      </Typography>
      
      {/* Quick Questions */}
      <Paper sx={{ p: 2, mb: 2 }}>
        <Typography variant="subtitle2" gutterBottom>
          Quick Questions:
        </Typography>
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          {quickQuestions.map((question, index) => (
            <Chip
              key={index}
              label={question}
              onClick={() => handleQuickQuestion(question)}
              sx={{ cursor: 'pointer' }}
              variant="outlined"
            />
          ))}
        </Box>
      </Paper>

      {/* Messages */}
      <Paper
        sx={{
          flexGrow: 1,
          p: 2,
          overflow: 'auto',
          maxHeight: 'calc(100vh - 300px)',
          mb: 2,
        }}
      >
        {messages.map((message) => (
          <MessageBubble key={message.id} message={message} />
        ))}
        
        {isLoading && (
          <Box sx={{ display: 'flex', justifyContent: 'flex-start', mb: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Avatar sx={{ bgcolor: 'primary.main', width: 32, height: 32, mr: 1 }}>
                <AIIcon sx={{ fontSize: 18 }} />
              </Avatar>
              <Paper sx={{ p: 2, display: 'flex', alignItems: 'center' }}>
                <CircularProgress size={20} sx={{ mr: 1 }} />
                <Typography variant="body2">AI is analyzing...</Typography>
              </Paper>
            </Box>
          </Box>
        )}
        
        <div ref={messagesEndRef} />
      </Paper>

      {/* Input */}
      <Box sx={{ display: 'flex', gap: 1 }}>
        <TextField
          fullWidth
          multiline
          maxRows={3}
          placeholder="Ask me anything about the event status..."
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyPress={handleKeyPress}
          disabled={isLoading}
        />
        <IconButton
          color="primary"
          onClick={handleSendMessage}
          disabled={!inputValue.trim() || isLoading}
          sx={{ alignSelf: 'flex-end' }}
        >
          <SendIcon />
        </IconButton>
      </Box>
    </Box>
  );
};

export default ChatInterface;
