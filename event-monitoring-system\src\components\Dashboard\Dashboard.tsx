import React, { useState } from 'react';
import {
  <PERSON>,
  Grid,
  <PERSON>ppB<PERSON>,
  <PERSON><PERSON><PERSON>,
  Ty<PERSON><PERSON>,
  IconButton,
  Badge,
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
} from '@mui/material';
import {
  Menu as MenuIcon,
  Notifications as NotificationsIcon,
  Dashboard as DashboardIcon,
  VideoCall as VideoIcon,
  Warning as IncidentIcon,
  Map as MapIcon,
  Chat as ChatIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material';
import { useSocket } from '../../contexts/SocketContext';
import VideoFeedGrid from '../VideoFeed/VideoFeedGrid';
import IncidentPanel from '../IncidentManagement/IncidentPanel';
import EventMap from '../Map/EventMap';
import ChatInterface from '../Chat/ChatInterface';
import StatsOverview from './StatsOverview';
import NotificationCenter from '../Layout/NotificationCenter';

const Dashboard: React.FC = () => {
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [activeView, setActiveView] = useState<'overview' | 'video' | 'incidents' | 'map' | 'chat'>('overview');
  const [notificationOpen, setNotificationOpen] = useState(false);
  const { connected, alerts } = useSocket();

  const unacknowledgedAlerts = alerts.filter(alert => !alert.acknowledged);

  const toggleDrawer = () => {
    setDrawerOpen(!drawerOpen);
  };

  const handleViewChange = (view: 'overview' | 'video' | 'incidents' | 'map' | 'chat') => {
    setActiveView(view);
    setDrawerOpen(false);
  };

  const renderMainContent = () => {
    switch (activeView) {
      case 'overview':
        return (
          <Grid container spacing={2} sx={{ height: '100%' }}>
            <Grid item xs={12}>
              <StatsOverview />
            </Grid>
            <Grid item xs={8}>
              <VideoFeedGrid maxFeeds={4} />
            </Grid>
            <Grid item xs={4}>
              <IncidentPanel compact />
            </Grid>
          </Grid>
        );
      case 'video':
        return <VideoFeedGrid />;
      case 'incidents':
        return <IncidentPanel />;
      case 'map':
        return <EventMap />;
      case 'chat':
        return <ChatInterface />;
      default:
        return <StatsOverview />;
    }
  };

  const menuItems = [
    { id: 'overview', label: 'Overview', icon: <DashboardIcon /> },
    { id: 'video', label: 'Video Feeds', icon: <VideoIcon /> },
    { id: 'incidents', label: 'Incidents', icon: <IncidentIcon /> },
    { id: 'map', label: 'Event Map', icon: <MapIcon /> },
    { id: 'chat', label: 'AI Assistant', icon: <ChatIcon /> },
  ];

  return (
    <Box sx={{ display: 'flex', height: '100vh' }}>
      {/* App Bar */}
      <AppBar position="fixed" sx={{ zIndex: (theme) => theme.zIndex.drawer + 1 }}>
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            onClick={toggleDrawer}
            edge="start"
            sx={{ mr: 2 }}
          >
            <MenuIcon />
          </IconButton>
          
          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
            Event Monitoring System
          </Typography>

          {/* Connection Status */}
          <Box sx={{ display: 'flex', alignItems: 'center', mr: 2 }}>
            <Box
              sx={{
                width: 8,
                height: 8,
                borderRadius: '50%',
                backgroundColor: connected ? '#4caf50' : '#f44336',
                mr: 1,
              }}
            />
            <Typography variant="body2">
              {connected ? 'Connected' : 'Disconnected'}
            </Typography>
          </Box>

          {/* Notifications */}
          <IconButton
            color="inherit"
            onClick={() => setNotificationOpen(true)}
          >
            <Badge badgeContent={unacknowledgedAlerts.length} color="error">
              <NotificationsIcon />
            </Badge>
          </IconButton>
        </Toolbar>
      </AppBar>

      {/* Navigation Drawer */}
      <Drawer
        variant="temporary"
        open={drawerOpen}
        onClose={toggleDrawer}
        ModalProps={{
          keepMounted: true,
        }}
        sx={{
          '& .MuiDrawer-paper': {
            width: 240,
            boxSizing: 'border-box',
          },
        }}
      >
        <Toolbar />
        <Box sx={{ overflow: 'auto' }}>
          <List>
            {menuItems.map((item) => (
              <ListItem
                button
                key={item.id}
                selected={activeView === item.id}
                onClick={() => handleViewChange(item.id as any)}
              >
                <ListItemIcon>{item.icon}</ListItemIcon>
                <ListItemText primary={item.label} />
              </ListItem>
            ))}
          </List>
          <Divider />
          <List>
            <ListItem button>
              <ListItemIcon>
                <SettingsIcon />
              </ListItemIcon>
              <ListItemText primary="Settings" />
            </ListItem>
          </List>
        </Box>
      </Drawer>

      {/* Main Content */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 2,
          mt: 8,
          height: 'calc(100vh - 64px)',
          overflow: 'auto',
        }}
      >
        {renderMainContent()}
      </Box>

      {/* Notification Center */}
      <NotificationCenter
        open={notificationOpen}
        onClose={() => setNotificationOpen(false)}
      />
    </Box>
  );
};

export default Dashboard;
