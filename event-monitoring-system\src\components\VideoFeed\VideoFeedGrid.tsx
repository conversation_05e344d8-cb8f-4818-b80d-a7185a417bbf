import React, { useState } from 'react';
import {
  Grid,
  Paper,
  Typography,
  Box,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  Fullscreen as FullscreenIcon,
  FiberManualRecord as RecordIcon,
  Warning as WarningIcon,
  ZoomIn as ZoomIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material';
import { useSocket } from '../../contexts/SocketContext';
import { VideoFeed } from '../../types';

interface VideoFeedCardProps {
  feed: VideoFeed;
  onFullscreen: (feed: VideoFeed) => void;
  compact?: boolean;
}

const VideoFeedCard: React.FC<VideoFeedCardProps> = ({ feed, onFullscreen, compact = false }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success';
      case 'inactive': return 'default';
      case 'error': return 'error';
      default: return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <RecordIcon sx={{ fontSize: 12 }} />;
      case 'error': return <WarningIcon sx={{ fontSize: 12 }} />;
      default: return null;
    }
  };

  return (
    <Paper
      sx={{
        position: 'relative',
        height: compact ? 200 : 300,
        overflow: 'hidden',
        backgroundColor: '#000',
        border: feed.status === 'error' ? '2px solid #f44336' : 'none',
      }}
    >
      {/* Video Stream Placeholder */}
      <Box
        sx={{
          width: '100%',
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: feed.status === 'active' 
            ? 'linear-gradient(45deg, #1a1a1a 25%, #2a2a2a 25%, #2a2a2a 50%, #1a1a1a 50%, #1a1a1a 75%, #2a2a2a 75%, #2a2a2a)'
            : '#333',
          backgroundSize: '20px 20px',
          position: 'relative',
        }}
      >
        {feed.status === 'active' ? (
          <Typography variant="h6" color="white" sx={{ textAlign: 'center' }}>
            LIVE FEED
            <br />
            <Typography variant="caption" component="div">
              {feed.name}
            </Typography>
          </Typography>
        ) : (
          <Typography variant="h6" color="text.secondary" sx={{ textAlign: 'center' }}>
            {feed.status === 'error' ? 'CONNECTION ERROR' : 'OFFLINE'}
            <br />
            <Typography variant="caption" component="div">
              {feed.name}
            </Typography>
          </Typography>
        )}
      </Box>

      {/* Overlay Controls */}
      <Box
        sx={{
          position: 'absolute',
          top: 8,
          left: 8,
          right: 8,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'flex-start',
        }}
      >
        <Chip
          icon={getStatusIcon(feed.status)}
          label={feed.status.toUpperCase()}
          color={getStatusColor(feed.status) as any}
          size="small"
          sx={{ backgroundColor: 'rgba(0,0,0,0.7)' }}
        />
        
        <Box>
          <IconButton
            size="small"
            sx={{ color: 'white', backgroundColor: 'rgba(0,0,0,0.5)' }}
            onClick={() => onFullscreen(feed)}
          >
            <FullscreenIcon />
          </IconButton>
        </Box>
      </Box>

      {/* Bottom Info */}
      <Box
        sx={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          background: 'linear-gradient(transparent, rgba(0,0,0,0.8))',
          p: 1,
        }}
      >
        <Typography variant="caption" color="white" display="block">
          {feed.location.zone}
        </Typography>
        <Typography variant="caption" color="rgba(255,255,255,0.7)" display="block">
          Last update: {new Date(feed.lastUpdate).toLocaleTimeString()}
        </Typography>
      </Box>
    </Paper>
  );
};

interface VideoFeedGridProps {
  maxFeeds?: number;
}

const VideoFeedGrid: React.FC<VideoFeedGridProps> = ({ maxFeeds }) => {
  const { videoFeeds } = useSocket();
  const [fullscreenFeed, setFullscreenFeed] = useState<VideoFeed | null>(null);
  const [settingsOpen, setSettingsOpen] = useState(false);
  const [selectedZone, setSelectedZone] = useState<string>('all');

  // Mock video feeds if none exist
  const mockFeeds: VideoFeed[] = [
    {
      id: '1',
      name: 'Main Stage Camera 1',
      url: 'rtmp://example.com/stream1',
      location: { lat: 40.7128, lng: -74.0060, zone: 'Main Stage' },
      status: 'active',
      lastUpdate: new Date(),
    },
    {
      id: '2',
      name: 'Entrance Gate A',
      url: 'rtmp://example.com/stream2',
      location: { lat: 40.7130, lng: -74.0058, zone: 'Entrance' },
      status: 'active',
      lastUpdate: new Date(),
    },
    {
      id: '3',
      name: 'Food Court Overview',
      url: 'rtmp://example.com/stream3',
      location: { lat: 40.7125, lng: -74.0065, zone: 'Food Court' },
      status: 'inactive',
      lastUpdate: new Date(Date.now() - 300000),
    },
    {
      id: '4',
      name: 'Emergency Exit B',
      url: 'rtmp://example.com/stream4',
      location: { lat: 40.7132, lng: -74.0055, zone: 'Emergency Exit' },
      status: 'error',
      lastUpdate: new Date(Date.now() - 600000),
    },
    {
      id: '5',
      name: 'Parking Area',
      url: 'rtmp://example.com/stream5',
      location: { lat: 40.7120, lng: -74.0070, zone: 'Parking' },
      status: 'active',
      lastUpdate: new Date(),
    },
    {
      id: '6',
      name: 'VIP Section',
      url: 'rtmp://example.com/stream6',
      location: { lat: 40.7135, lng: -74.0052, zone: 'VIP Area' },
      status: 'active',
      lastUpdate: new Date(),
    },
  ];

  const feeds = videoFeeds.length > 0 ? videoFeeds : mockFeeds;
  const filteredFeeds = selectedZone === 'all' 
    ? feeds 
    : feeds.filter(feed => feed.location.zone === selectedZone);
  
  const displayFeeds = maxFeeds ? filteredFeeds.slice(0, maxFeeds) : filteredFeeds;

  const zones = Array.from(new Set(feeds.map(feed => feed.location.zone)));

  const handleFullscreen = (feed: VideoFeed) => {
    setFullscreenFeed(feed);
  };

  const closeFullscreen = () => {
    setFullscreenFeed(null);
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">
          Video Feeds {maxFeeds && `(${displayFeeds.length}/${maxFeeds})`}
        </Typography>
        
        <Box sx={{ display: 'flex', gap: 1 }}>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Zone</InputLabel>
            <Select
              value={selectedZone}
              label="Zone"
              onChange={(e) => setSelectedZone(e.target.value)}
            >
              <MenuItem value="all">All Zones</MenuItem>
              {zones.map(zone => (
                <MenuItem key={zone} value={zone}>{zone}</MenuItem>
              ))}
            </Select>
          </FormControl>
          
          <IconButton onClick={() => setSettingsOpen(true)}>
            <SettingsIcon />
          </IconButton>
        </Box>
      </Box>

      {/* Video Grid */}
      <Grid container spacing={2}>
        {displayFeeds.map((feed) => (
          <Grid item xs={12} sm={6} md={maxFeeds ? 6 : 4} lg={maxFeeds ? 6 : 3} key={feed.id}>
            <VideoFeedCard
              feed={feed}
              onFullscreen={handleFullscreen}
              compact={!!maxFeeds}
            />
          </Grid>
        ))}
      </Grid>

      {/* Fullscreen Dialog */}
      <Dialog
        open={!!fullscreenFeed}
        onClose={closeFullscreen}
        maxWidth="lg"
        fullWidth
        PaperProps={{
          sx: { backgroundColor: '#000', minHeight: '80vh' }
        }}
      >
        {fullscreenFeed && (
          <>
            <DialogTitle sx={{ color: 'white' }}>
              {fullscreenFeed.name} - {fullscreenFeed.location.zone}
            </DialogTitle>
            <DialogContent>
              <VideoFeedCard
                feed={fullscreenFeed}
                onFullscreen={() => {}}
                compact={false}
              />
            </DialogContent>
            <DialogActions>
              <Button onClick={closeFullscreen} color="primary">
                Close
              </Button>
            </DialogActions>
          </>
        )}
      </Dialog>
    </Box>
  );
};

export default VideoFeedGrid;
