{"version": 3, "sources": ["../../@mui/material/esm/CssBaseline/CssBaseline.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { globalCss } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\n\n// to determine if the global styles are static or dynamic\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst isDynamicSupport = typeof globalCss({}) === 'function';\nexport const html = (theme, enableColorScheme) => ({\n  WebkitFontSmoothing: 'antialiased',\n  // Antialiasing.\n  MozOsxFontSmoothing: 'grayscale',\n  // Antialiasing.\n  // Change from `box-sizing: content-box` so that `width`\n  // is not affected by `padding` or `border`.\n  boxSizing: 'border-box',\n  // Fix font resize problem in iOS\n  WebkitTextSizeAdjust: '100%',\n  // When used under CssVarsProvider, colorScheme should not be applied dynamically because it will generate the stylesheet twice for server-rendered applications.\n  ...(enableColorScheme && !theme.vars && {\n    colorScheme: theme.palette.mode\n  })\n});\nexport const body = theme => ({\n  color: (theme.vars || theme).palette.text.primary,\n  ...theme.typography.body1,\n  backgroundColor: (theme.vars || theme).palette.background.default,\n  '@media print': {\n    // Save printer ink.\n    backgroundColor: (theme.vars || theme).palette.common.white\n  }\n});\nexport const styles = (theme, enableColorScheme = false) => {\n  const colorSchemeStyles = {};\n  if (enableColorScheme && theme.colorSchemes && typeof theme.getColorSchemeSelector === 'function') {\n    Object.entries(theme.colorSchemes).forEach(([key, scheme]) => {\n      const selector = theme.getColorSchemeSelector(key);\n      if (selector.startsWith('@')) {\n        // for @media (prefers-color-scheme), we need to target :root\n        colorSchemeStyles[selector] = {\n          ':root': {\n            colorScheme: scheme.palette?.mode\n          }\n        };\n      } else {\n        // else, it's likely that the selector already target an element with a class or data attribute\n        colorSchemeStyles[selector.replace(/\\s*&/, '')] = {\n          colorScheme: scheme.palette?.mode\n        };\n      }\n    });\n  }\n  let defaultStyles = {\n    html: html(theme, enableColorScheme),\n    '*, *::before, *::after': {\n      boxSizing: 'inherit'\n    },\n    'strong, b': {\n      fontWeight: theme.typography.fontWeightBold\n    },\n    body: {\n      margin: 0,\n      // Remove the margin in all browsers.\n      ...body(theme),\n      // Add support for document.body.requestFullScreen().\n      // Other elements, if background transparent, are not supported.\n      '&::backdrop': {\n        backgroundColor: (theme.vars || theme).palette.background.default\n      }\n    },\n    ...colorSchemeStyles\n  };\n  const themeOverrides = theme.components?.MuiCssBaseline?.styleOverrides;\n  if (themeOverrides) {\n    defaultStyles = [defaultStyles, themeOverrides];\n  }\n  return defaultStyles;\n};\n\n// `ecs` stands for enableColorScheme. This is internal logic to make it work with Pigment CSS, so shorter is better.\nconst SELECTOR = 'mui-ecs';\nconst staticStyles = theme => {\n  const result = styles(theme, false);\n  const baseStyles = Array.isArray(result) ? result[0] : result;\n  if (!theme.vars && baseStyles) {\n    baseStyles.html[`:root:has(${SELECTOR})`] = {\n      colorScheme: theme.palette.mode\n    };\n  }\n  if (theme.colorSchemes) {\n    Object.entries(theme.colorSchemes).forEach(([key, scheme]) => {\n      const selector = theme.getColorSchemeSelector(key);\n      if (selector.startsWith('@')) {\n        // for @media (prefers-color-scheme), we need to target :root\n        baseStyles[selector] = {\n          [`:root:not(:has(.${SELECTOR}))`]: {\n            colorScheme: scheme.palette?.mode\n          }\n        };\n      } else {\n        // else, it's likely that the selector already target an element with a class or data attribute\n        baseStyles[selector.replace(/\\s*&/, '')] = {\n          [`&:not(:has(.${SELECTOR}))`]: {\n            colorScheme: scheme.palette?.mode\n          }\n        };\n      }\n    });\n  }\n  return result;\n};\nconst GlobalStyles = globalCss(isDynamicSupport ? ({\n  theme,\n  enableColorScheme\n}) => styles(theme, enableColorScheme) : ({\n  theme\n}) => staticStyles(theme));\n\n/**\n * Kickstart an elegant, consistent, and simple baseline to build upon.\n */\nfunction CssBaseline(inProps) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiCssBaseline'\n  });\n  const {\n    children,\n    enableColorScheme = false\n  } = props;\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [isDynamicSupport && /*#__PURE__*/_jsx(GlobalStyles, {\n      enableColorScheme: enableColorScheme\n    }), !isDynamicSupport && !enableColorScheme && /*#__PURE__*/_jsx(\"span\", {\n      className: SELECTOR,\n      style: {\n        display: 'none'\n      }\n    }), children]\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? CssBaseline.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * You can wrap a node.\n   */\n  children: PropTypes.node,\n  /**\n   * Enable `color-scheme` CSS property to use `theme.palette.mode`.\n   * For more details, check out https://developer.mozilla.org/en-US/docs/Web/CSS/color-scheme\n   * For browser support, check out https://caniuse.com/?search=color-scheme\n   * @default false\n   */\n  enableColorScheme: PropTypes.bool\n} : void 0;\nexport default CssBaseline;"], "mappings": ";;;;;;;;;;;;;;;;AAEA,YAAuB;AACvB,wBAAsB;AAKtB,yBAA2C;AAC3C,IAAM,mBAAmB,OAAO,UAAU,CAAC,CAAC,MAAM;AAC3C,IAAM,OAAO,CAAC,OAAO,uBAAuB;AAAA,EACjD,qBAAqB;AAAA;AAAA,EAErB,qBAAqB;AAAA;AAAA;AAAA;AAAA,EAIrB,WAAW;AAAA;AAAA,EAEX,sBAAsB;AAAA;AAAA,EAEtB,GAAI,qBAAqB,CAAC,MAAM,QAAQ;AAAA,IACtC,aAAa,MAAM,QAAQ;AAAA,EAC7B;AACF;AACO,IAAM,OAAO,YAAU;AAAA,EAC5B,QAAQ,MAAM,QAAQ,OAAO,QAAQ,KAAK;AAAA,EAC1C,GAAG,MAAM,WAAW;AAAA,EACpB,kBAAkB,MAAM,QAAQ,OAAO,QAAQ,WAAW;AAAA,EAC1D,gBAAgB;AAAA;AAAA,IAEd,kBAAkB,MAAM,QAAQ,OAAO,QAAQ,OAAO;AAAA,EACxD;AACF;AACO,IAAM,SAAS,CAAC,OAAO,oBAAoB,UAAU;AAC1D,QAAM,oBAAoB,CAAC;AAC3B,MAAI,qBAAqB,MAAM,gBAAgB,OAAO,MAAM,2BAA2B,YAAY;AACjG,WAAO,QAAQ,MAAM,YAAY,EAAE,QAAQ,CAAC,CAAC,KAAK,MAAM,MAAM;AAC5D,YAAM,WAAW,MAAM,uBAAuB,GAAG;AACjD,UAAI,SAAS,WAAW,GAAG,GAAG;AAE5B,0BAAkB,QAAQ,IAAI;AAAA,UAC5B,SAAS;AAAA,YACP,aAAa,OAAO,SAAS;AAAA,UAC/B;AAAA,QACF;AAAA,MACF,OAAO;AAEL,0BAAkB,SAAS,QAAQ,QAAQ,EAAE,CAAC,IAAI;AAAA,UAChD,aAAa,OAAO,SAAS;AAAA,QAC/B;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACA,MAAI,gBAAgB;AAAA,IAClB,MAAM,KAAK,OAAO,iBAAiB;AAAA,IACnC,0BAA0B;AAAA,MACxB,WAAW;AAAA,IACb;AAAA,IACA,aAAa;AAAA,MACX,YAAY,MAAM,WAAW;AAAA,IAC/B;AAAA,IACA,MAAM;AAAA,MACJ,QAAQ;AAAA;AAAA,MAER,GAAG,KAAK,KAAK;AAAA;AAAA;AAAA,MAGb,eAAe;AAAA,QACb,kBAAkB,MAAM,QAAQ,OAAO,QAAQ,WAAW;AAAA,MAC5D;AAAA,IACF;AAAA,IACA,GAAG;AAAA,EACL;AACA,QAAM,iBAAiB,MAAM,YAAY,gBAAgB;AACzD,MAAI,gBAAgB;AAClB,oBAAgB,CAAC,eAAe,cAAc;AAAA,EAChD;AACA,SAAO;AACT;AAGA,IAAM,WAAW;AACjB,IAAM,eAAe,WAAS;AAC5B,QAAM,SAAS,OAAO,OAAO,KAAK;AAClC,QAAM,aAAa,MAAM,QAAQ,MAAM,IAAI,OAAO,CAAC,IAAI;AACvD,MAAI,CAAC,MAAM,QAAQ,YAAY;AAC7B,eAAW,KAAK,aAAa,QAAQ,GAAG,IAAI;AAAA,MAC1C,aAAa,MAAM,QAAQ;AAAA,IAC7B;AAAA,EACF;AACA,MAAI,MAAM,cAAc;AACtB,WAAO,QAAQ,MAAM,YAAY,EAAE,QAAQ,CAAC,CAAC,KAAK,MAAM,MAAM;AAC5D,YAAM,WAAW,MAAM,uBAAuB,GAAG;AACjD,UAAI,SAAS,WAAW,GAAG,GAAG;AAE5B,mBAAW,QAAQ,IAAI;AAAA,UACrB,CAAC,mBAAmB,QAAQ,IAAI,GAAG;AAAA,YACjC,aAAa,OAAO,SAAS;AAAA,UAC/B;AAAA,QACF;AAAA,MACF,OAAO;AAEL,mBAAW,SAAS,QAAQ,QAAQ,EAAE,CAAC,IAAI;AAAA,UACzC,CAAC,eAAe,QAAQ,IAAI,GAAG;AAAA,YAC7B,aAAa,OAAO,SAAS;AAAA,UAC/B;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO;AACT;AACA,IAAM,eAAe,UAAU,mBAAmB,CAAC;AAAA,EACjD;AAAA,EACA;AACF,MAAM,OAAO,OAAO,iBAAiB,IAAI,CAAC;AAAA,EACxC;AACF,MAAM,aAAa,KAAK,CAAC;AAKzB,SAAS,YAAY,SAAS;AAC5B,QAAM,QAAQ,gBAAgB;AAAA,IAC5B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM;AAAA,IACJ;AAAA,IACA,oBAAoB;AAAA,EACtB,IAAI;AACJ,aAAoB,mBAAAA,MAAY,gBAAU;AAAA,IACxC,UAAU,CAAC,wBAAiC,mBAAAC,KAAK,cAAc;AAAA,MAC7D;AAAA,IACF,CAAC,GAAG,CAAC,oBAAoB,CAAC,yBAAkC,mBAAAA,KAAK,QAAQ;AAAA,MACvE,WAAW;AAAA,MACX,OAAO;AAAA,QACL,SAAS;AAAA,MACX;AAAA,IACF,CAAC,GAAG,QAAQ;AAAA,EACd,CAAC;AACH;AACA,OAAwC,YAAY,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQrF,UAAU,kBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOpB,mBAAmB,kBAAAA,QAAU;AAC/B,IAAI;AACJ,IAAO,sBAAQ;", "names": ["_jsxs", "_jsx", "PropTypes"]}