/* Global styles for Event Monitoring System */
@import 'leaflet/dist/leaflet.css';

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
  background-color: #0a0e1a;
  color: #ffffff;
  overflow: hidden;
}

#root {
  width: 100vw;
  height: 100vh;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1a1a1a;
}

::-webkit-scrollbar-thumb {
  background: #444;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #666;
}

/* Material-UI theme overrides */
.MuiPaper-root {
  background-color: #1a1a1a !important;
}

.MuiAppBar-root {
  background-color: #0d1421 !important;
}

/* Custom utility classes */
.status-active {
  color: #4caf50;
}

.status-warning {
  color: #ff9800;
}

.status-critical {
  color: #f44336;
}

.status-inactive {
  color: #757575;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
}
