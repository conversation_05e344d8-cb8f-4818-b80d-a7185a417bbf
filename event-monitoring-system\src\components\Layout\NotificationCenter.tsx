import React, { useState, useEffect } from 'react';
import {
  Box,
  Drawer,
  Typography,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Chip,
  Button,
  Divider,
  Badge,
  Tooltip,
} from '@mui/material';
import {
  Notifications as NotificationsIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  Info as InfoIcon,
  CheckCircle as CheckIcon,
  Close as CloseIcon,
  MarkEmailRead as MarkReadIcon,
} from '@mui/icons-material';
import { useSocket } from '../../contexts/SocketContext';
import { useNotifications } from '../../hooks/useNotifications';
import { SystemAlert } from '../../types';

interface NotificationCenterProps {
  open: boolean;
  onClose: () => void;
}

const NotificationCenter: React.FC<NotificationCenterProps> = ({ open, onClose }) => {
  const { alerts, acknowledgeAlert } = useSocket();
  const { showAlertNotification, requestPermission, permission } = useNotifications();
  const [lastAlertCount, setLastAlertCount] = useState(0);

  // Request notification permission on mount
  useEffect(() => {
    if (permission === 'default') {
      requestPermission();
    }
  }, [permission, requestPermission]);

  // Show browser notifications for new alerts
  useEffect(() => {
    const unacknowledgedAlerts = alerts.filter(alert => !alert.acknowledged);
    
    if (unacknowledgedAlerts.length > lastAlertCount) {
      const newAlerts = unacknowledgedAlerts.slice(lastAlertCount);
      newAlerts.forEach(alert => {
        if (alert.severity === 'critical' || alert.severity === 'error') {
          showAlertNotification(alert);
        }
      });
    }
    
    setLastAlertCount(unacknowledgedAlerts.length);
  }, [alerts, lastAlertCount, showAlertNotification]);

  const getAlertIcon = (severity: string) => {
    switch (severity) {
      case 'critical':
      case 'error':
        return <ErrorIcon color="error" />;
      case 'warning':
        return <WarningIcon color="warning" />;
      case 'info':
        return <InfoIcon color="info" />;
      default:
        return <InfoIcon />;
    }
  };

  const getAlertColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'error';
      case 'error':
        return 'error';
      case 'warning':
        return 'warning';
      case 'info':
        return 'info';
      default:
        return 'default';
    }
  };

  const handleAcknowledge = (alertId: string) => {
    acknowledgeAlert(alertId);
  };

  const handleAcknowledgeAll = () => {
    const unacknowledgedAlerts = alerts.filter(alert => !alert.acknowledged);
    unacknowledgedAlerts.forEach(alert => {
      acknowledgeAlert(alert.id);
    });
  };

  const unacknowledgedAlerts = alerts.filter(alert => !alert.acknowledged);
  const acknowledgedAlerts = alerts.filter(alert => alert.acknowledged);

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours}h ago`;
    return date.toLocaleDateString();
  };

  return (
    <Drawer
      anchor="right"
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: { width: 400, maxWidth: '90vw' }
      }}
    >
      <Box sx={{ p: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6">
            Notifications
          </Typography>
          <Box>
            {unacknowledgedAlerts.length > 0 && (
              <Button
                size="small"
                startIcon={<MarkReadIcon />}
                onClick={handleAcknowledgeAll}
                sx={{ mr: 1 }}
              >
                Mark All Read
              </Button>
            )}
            <IconButton onClick={onClose}>
              <CloseIcon />
            </IconButton>
          </Box>
        </Box>

        {/* Summary */}
        <Box sx={{ mb: 2 }}>
          <Typography variant="body2" color="text.secondary">
            {unacknowledgedAlerts.length} unread, {alerts.length} total
          </Typography>
        </Box>

        {/* Unacknowledged Alerts */}
        {unacknowledgedAlerts.length > 0 && (
          <>
            <Typography variant="subtitle2" gutterBottom>
              Unread Alerts
            </Typography>
            <List dense>
              {unacknowledgedAlerts.map((alert) => (
                <ListItem
                  key={alert.id}
                  sx={{
                    border: 1,
                    borderColor: getAlertColor(alert.severity) + '.main',
                    borderRadius: 1,
                    mb: 1,
                    backgroundColor: getAlertColor(alert.severity) + '.light',
                    opacity: 0.1,
                  }}
                >
                  <ListItemIcon>
                    {getAlertIcon(alert.severity)}
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      <Box>
                        <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                          {alert.type.toUpperCase()}
                        </Typography>
                        <Typography variant="body2">
                          {alert.message}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {formatTimeAgo(alert.timestamp)}
                        </Typography>
                      </Box>
                    }
                  />
                  <ListItemSecondaryAction>
                    <Tooltip title="Acknowledge">
                      <IconButton
                        edge="end"
                        size="small"
                        onClick={() => handleAcknowledge(alert.id)}
                      >
                        <CheckIcon />
                      </IconButton>
                    </Tooltip>
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
            </List>
            <Divider sx={{ my: 2 }} />
          </>
        )}

        {/* Acknowledged Alerts */}
        {acknowledgedAlerts.length > 0 && (
          <>
            <Typography variant="subtitle2" gutterBottom>
              Recent Activity
            </Typography>
            <List dense>
              {acknowledgedAlerts.slice(0, 10).map((alert) => (
                <ListItem key={alert.id} sx={{ opacity: 0.6 }}>
                  <ListItemIcon>
                    {getAlertIcon(alert.severity)}
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      <Box>
                        <Typography variant="body2">
                          {alert.message}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {formatTimeAgo(alert.timestamp)} • Acknowledged by {alert.acknowledgedBy}
                        </Typography>
                      </Box>
                    }
                  />
                  <ListItemSecondaryAction>
                    <CheckIcon color="success" fontSize="small" />
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
            </List>
          </>
        )}

        {/* Empty State */}
        {alerts.length === 0 && (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <NotificationsIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" color="text.secondary">
              No notifications
            </Typography>
            <Typography variant="body2" color="text.secondary">
              All systems operating normally
            </Typography>
          </Box>
        )}
      </Box>
    </Drawer>
  );
};

export default NotificationCenter;
