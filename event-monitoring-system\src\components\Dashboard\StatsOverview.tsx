import React from 'react';
import {
  Grid,
  Paper,
  Typography,
  Box,
  LinearProgress,
  Chip,
} from '@mui/material';
import {
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  VideoCall as VideoIcon,
  Security as SecurityIcon,
  People as PeopleIcon,
  LocationOn as LocationIcon,
} from '@mui/icons-material';
import { useSocket } from '../../contexts/SocketContext';

interface StatCardProps {
  title: string;
  value: number;
  total?: number;
  icon: React.ReactNode;
  color: 'primary' | 'secondary' | 'error' | 'warning' | 'success';
  subtitle?: string;
}

const StatCard: React.FC<StatCardProps> = ({ title, value, total, icon, color, subtitle }) => {
  const percentage = total ? (value / total) * 100 : 0;
  
  return (
    <Paper
      sx={{
        p: 2,
        display: 'flex',
        flexDirection: 'column',
        height: 140,
        position: 'relative',
        overflow: 'hidden',
      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
        <Box sx={{ color: `${color}.main`, mr: 1 }}>
          {icon}
        </Box>
        <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
          {title}
        </Typography>
      </Box>
      
      <Typography variant="h3" component="div" sx={{ mb: 1 }}>
        {value}
        {total && (
          <Typography variant="h6" component="span" sx={{ color: 'text.secondary', ml: 1 }}>
            / {total}
          </Typography>
        )}
      </Typography>
      
      {subtitle && (
        <Typography variant="body2" color="text.secondary">
          {subtitle}
        </Typography>
      )}
      
      {total && (
        <Box sx={{ mt: 'auto' }}>
          <LinearProgress
            variant="determinate"
            value={percentage}
            color={color}
            sx={{ height: 6, borderRadius: 3 }}
          />
        </Box>
      )}
    </Paper>
  );
};

const StatsOverview: React.FC = () => {
  const { incidents, responseUnits, videoFeeds } = useSocket();

  // Calculate statistics
  const activeIncidents = incidents.filter(i => i.status !== 'resolved').length;
  const criticalIncidents = incidents.filter(i => i.severity === 'critical').length;
  const resolvedIncidents = incidents.filter(i => i.status === 'resolved').length;
  
  const activeVideoFeeds = videoFeeds.filter(v => v.status === 'active').length;
  const totalVideoFeeds = videoFeeds.length;
  
  const availableUnits = responseUnits.filter(u => u.status === 'available').length;
  const totalUnits = responseUnits.length;
  
  const dispatchedUnits = responseUnits.filter(u => u.status === 'dispatched').length;

  // Mock data for zones (would come from API in real implementation)
  const totalZones = 12;
  const criticalZones = 2;

  const getSystemHealthStatus = () => {
    if (criticalIncidents > 0 || criticalZones > 0) return 'critical';
    if (activeIncidents > 5 || availableUnits < totalUnits * 0.3) return 'warning';
    return 'healthy';
  };

  const systemHealth = getSystemHealthStatus();

  return (
    <Box>
      <Typography variant="h5" gutterBottom sx={{ mb: 3 }}>
        System Overview
      </Typography>
      
      <Grid container spacing={3}>
        {/* Incidents */}
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Active Incidents"
            value={activeIncidents}
            total={incidents.length}
            icon={<WarningIcon />}
            color={criticalIncidents > 0 ? 'error' : activeIncidents > 0 ? 'warning' : 'success'}
            subtitle={`${criticalIncidents} critical`}
          />
        </Grid>

        {/* Video Feeds */}
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Video Feeds"
            value={activeVideoFeeds}
            total={totalVideoFeeds}
            icon={<VideoIcon />}
            color={activeVideoFeeds === totalVideoFeeds ? 'success' : 'warning'}
            subtitle="Online feeds"
          />
        </Grid>

        {/* Response Units */}
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Available Units"
            value={availableUnits}
            total={totalUnits}
            icon={<SecurityIcon />}
            color={availableUnits > totalUnits * 0.5 ? 'success' : 'warning'}
            subtitle={`${dispatchedUnits} dispatched`}
          />
        </Grid>

        {/* Event Zones */}
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Event Zones"
            value={totalZones - criticalZones}
            total={totalZones}
            icon={<LocationIcon />}
            color={criticalZones === 0 ? 'success' : 'error'}
            subtitle={`${criticalZones} critical`}
          />
        </Grid>

        {/* System Health */}
        <Grid item xs={12}>
          <Paper sx={{ p: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Typography variant="h6">
                System Health Status
              </Typography>
              <Chip
                icon={systemHealth === 'healthy' ? <CheckCircleIcon /> : <WarningIcon />}
                label={systemHealth.toUpperCase()}
                color={
                  systemHealth === 'healthy' ? 'success' :
                  systemHealth === 'warning' ? 'warning' : 'error'
                }
                variant="filled"
              />
            </Box>
            
            <Box sx={{ mt: 2, display: 'flex', gap: 2, flexWrap: 'wrap' }}>
              <Typography variant="body2" color="text.secondary">
                Last updated: {new Date().toLocaleTimeString()}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total incidents today: {incidents.length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Resolved incidents: {resolvedIncidents}
              </Typography>
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default StatsOverview;
