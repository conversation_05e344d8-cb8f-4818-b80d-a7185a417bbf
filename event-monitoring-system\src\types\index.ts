// Core types for the Event Monitoring System

export interface VideoFeed {
  id: string;
  name: string;
  url: string;
  location: {
    lat: number;
    lng: number;
    zone: string;
  };
  status: 'active' | 'inactive' | 'error';
  lastUpdate: Date;
}

export interface Incident {
  id: string;
  type: 'crowd_surge' | 'fire' | 'medical' | 'security' | 'other';
  severity: 'low' | 'medium' | 'high' | 'critical';
  status: 'detected' | 'verified' | 'responding' | 'resolved';
  location: {
    lat: number;
    lng: number;
    zone: string;
    description: string;
  };
  timestamp: Date;
  description: string;
  confidence: number;
  videoFeedId: string;
  assignedUnits: string[];
  aiAnalysis?: {
    summary: string;
    keyFrames: string[];
    detectionDetails: any;
  };
  humanVerification?: {
    verified: boolean;
    verifiedBy: string;
    verificationTime: Date;
    notes: string;
  };
}

export interface ResponseUnit {
  id: string;
  type: 'medical' | 'fire' | 'security' | 'police';
  name: string;
  status: 'available' | 'dispatched' | 'busy' | 'offline';
  location: {
    lat: number;
    lng: number;
  };
  assignedIncident?: string;
  estimatedArrival?: Date;
  route?: {
    lat: number;
    lng: number;
  }[];
}

export interface EventZone {
  id: string;
  name: string;
  type: 'stage' | 'entrance' | 'exit' | 'food' | 'medical' | 'security' | 'parking';
  coordinates: {
    lat: number;
    lng: number;
  }[];
  capacity: number;
  currentOccupancy: number;
  status: 'normal' | 'crowded' | 'critical' | 'closed';
  videoFeeds: string[];
}

export interface SystemAlert {
  id: string;
  type: 'system' | 'incident' | 'unit' | 'zone';
  severity: 'info' | 'warning' | 'error' | 'critical';
  message: string;
  timestamp: Date;
  acknowledged: boolean;
  acknowledgedBy?: string;
}

export interface ChatMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
  relatedIncident?: string;
  attachments?: {
    type: 'image' | 'video' | 'report';
    url: string;
    name: string;
  }[];
}

export interface DashboardStats {
  totalIncidents: number;
  activeIncidents: number;
  resolvedIncidents: number;
  totalVideoFeeds: number;
  activeVideoFeeds: number;
  totalResponseUnits: number;
  availableResponseUnits: number;
  totalZones: number;
  criticalZones: number;
  systemHealth: 'healthy' | 'warning' | 'critical';
}

export interface User {
  id: string;
  name: string;
  role: 'operator' | 'supervisor' | 'admin';
  permissions: string[];
  lastLogin: Date;
}

// WebSocket message types
export interface SocketMessage {
  type: 'incident_update' | 'video_feed_update' | 'unit_update' | 'zone_update' | 'system_alert';
  payload: any;
  timestamp: Date;
}

// API Response types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Map related types
export interface MapBounds {
  north: number;
  south: number;
  east: number;
  west: number;
}

export interface MapMarker {
  id: string;
  type: 'incident' | 'unit' | 'camera' | 'zone';
  position: {
    lat: number;
    lng: number;
  };
  data: any;
  icon?: string;
  color?: string;
}
