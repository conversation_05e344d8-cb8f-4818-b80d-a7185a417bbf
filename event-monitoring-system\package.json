{"name": "event-monitoring-system", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "devDependencies": {"typescript": "~5.8.3", "vite": "^7.0.4"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@hookform/resolvers": "^5.2.1", "@mui/icons-material": "^7.2.0", "@mui/material": "^7.2.0", "@mui/x-data-grid": "^8.9.2", "@types/leaflet": "^1.9.20", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "axios": "^1.11.0", "leaflet": "^1.9.4", "react": "^19.1.1", "react-dom": "^19.1.1", "react-hook-form": "^7.61.1", "react-leaflet": "^5.0.0", "react-router-dom": "^7.7.1", "recharts": "^3.1.0", "socket.io-client": "^4.8.1", "yup": "^1.6.1"}}