import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { io, Socket } from 'socket.io-client';
import { SocketMessage, Incident, ResponseUnit, VideoFeed, SystemAlert } from '../types';
import { generateMockIncidents, generateMockResponseUnits, generateMockVideoFeeds, generateMockAlerts } from '../utils/mockData';

interface SocketContextType {
  socket: Socket | null;
  connected: boolean;
  incidents: Incident[];
  responseUnits: ResponseUnit[];
  videoFeeds: VideoFeed[];
  alerts: SystemAlert[];
  sendMessage: (message: any) => void;
  acknowledgeAlert: (alertId: string) => void;
}

const SocketContext = createContext<SocketContextType | undefined>(undefined);

interface SocketProviderProps {
  children: ReactNode;
}

export const SocketProvider: React.FC<SocketProviderProps> = ({ children }) => {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [connected, setConnected] = useState(false);
  const [incidents, setIncidents] = useState<Incident[]>(generateMockIncidents());
  const [responseUnits, setResponseUnits] = useState<ResponseUnit[]>(generateMockResponseUnits());
  const [videoFeeds, setVideoFeeds] = useState<VideoFeed[]>(generateMockVideoFeeds());
  const [alerts, setAlerts] = useState<SystemAlert[]>(generateMockAlerts());

  useEffect(() => {
    // Initialize socket connection
    const newSocket = io(process.env.REACT_APP_SOCKET_URL || 'ws://localhost:3001', {
      transports: ['websocket'],
      autoConnect: true,
    });

    newSocket.on('connect', () => {
      console.log('Connected to server');
      setConnected(true);
    });

    newSocket.on('disconnect', () => {
      console.log('Disconnected from server');
      setConnected(false);
    });

    // Handle incoming messages
    newSocket.on('incident_update', (data: Incident) => {
      setIncidents(prev => {
        const existingIndex = prev.findIndex(incident => incident.id === data.id);
        if (existingIndex >= 0) {
          const updated = [...prev];
          updated[existingIndex] = data;
          return updated;
        } else {
          return [...prev, data];
        }
      });
    });

    newSocket.on('unit_update', (data: ResponseUnit) => {
      setResponseUnits(prev => {
        const existingIndex = prev.findIndex(unit => unit.id === data.id);
        if (existingIndex >= 0) {
          const updated = [...prev];
          updated[existingIndex] = data;
          return updated;
        } else {
          return [...prev, data];
        }
      });
    });

    newSocket.on('video_feed_update', (data: VideoFeed) => {
      setVideoFeeds(prev => {
        const existingIndex = prev.findIndex(feed => feed.id === data.id);
        if (existingIndex >= 0) {
          const updated = [...prev];
          updated[existingIndex] = data;
          return updated;
        } else {
          return [...prev, data];
        }
      });
    });

    newSocket.on('system_alert', (data: SystemAlert) => {
      setAlerts(prev => [data, ...prev.slice(0, 49)]); // Keep last 50 alerts
    });

    setSocket(newSocket);

    // Simulate real-time updates for demonstration
    const simulateUpdates = () => {
      // Simulate new alerts occasionally
      if (Math.random() < 0.1) { // 10% chance every 5 seconds
        const newAlert: SystemAlert = {
          id: `alert-${Date.now()}`,
          type: Math.random() < 0.5 ? 'system' : 'incident',
          severity: Math.random() < 0.2 ? 'critical' : Math.random() < 0.4 ? 'warning' : 'info',
          message: `Simulated alert at ${new Date().toLocaleTimeString()}`,
          timestamp: new Date(),
          acknowledged: false,
        };
        setAlerts(prev => [newAlert, ...prev.slice(0, 19)]); // Keep last 20 alerts
      }

      // Update video feed status randomly
      setVideoFeeds(prev => prev.map(feed => ({
        ...feed,
        lastUpdate: Math.random() < 0.8 ? new Date() : feed.lastUpdate,
        status: Math.random() < 0.95 ? 'active' : Math.random() < 0.5 ? 'inactive' : 'error',
      })));
    };

    const updateInterval = setInterval(simulateUpdates, 5000); // Update every 5 seconds

    // Cleanup on unmount
    return () => {
      newSocket.close();
      clearInterval(updateInterval);
    };
  }, []);

  const sendMessage = (message: any) => {
    if (socket && connected) {
      socket.emit('message', message);
    }
  };

  const acknowledgeAlert = (alertId: string) => {
    setAlerts(prev => 
      prev.map(alert => 
        alert.id === alertId 
          ? { ...alert, acknowledged: true, acknowledgedBy: 'current_user' }
          : alert
      )
    );
    
    if (socket && connected) {
      socket.emit('acknowledge_alert', { alertId });
    }
  };

  const value: SocketContextType = {
    socket,
    connected,
    incidents,
    responseUnits,
    videoFeeds,
    alerts,
    sendMessage,
    acknowledgeAlert,
  };

  return (
    <SocketContext.Provider value={value}>
      {children}
    </SocketContext.Provider>
  );
};

export const useSocket = (): SocketContextType => {
  const context = useContext(SocketContext);
  if (context === undefined) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  return context;
};
