import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { io, Socket } from 'socket.io-client';
import { SocketMessage, Incident, ResponseUnit, VideoFeed, SystemAlert } from '../types';

interface SocketContextType {
  socket: Socket | null;
  connected: boolean;
  incidents: Incident[];
  responseUnits: ResponseUnit[];
  videoFeeds: VideoFeed[];
  alerts: SystemAlert[];
  sendMessage: (message: any) => void;
  acknowledgeAlert: (alertId: string) => void;
}

const SocketContext = createContext<SocketContextType | undefined>(undefined);

interface SocketProviderProps {
  children: ReactNode;
}

export const SocketProvider: React.FC<SocketProviderProps> = ({ children }) => {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [connected, setConnected] = useState(false);
  const [incidents, setIncidents] = useState<Incident[]>([]);
  const [responseUnits, setResponseUnits] = useState<ResponseUnit[]>([]);
  const [videoFeeds, setVideoFeeds] = useState<VideoFeed[]>([]);
  const [alerts, setAlerts] = useState<SystemAlert[]>([]);

  useEffect(() => {
    // Initialize socket connection
    const newSocket = io(process.env.REACT_APP_SOCKET_URL || 'ws://localhost:3001', {
      transports: ['websocket'],
      autoConnect: true,
    });

    newSocket.on('connect', () => {
      console.log('Connected to server');
      setConnected(true);
    });

    newSocket.on('disconnect', () => {
      console.log('Disconnected from server');
      setConnected(false);
    });

    // Handle incoming messages
    newSocket.on('incident_update', (data: Incident) => {
      setIncidents(prev => {
        const existingIndex = prev.findIndex(incident => incident.id === data.id);
        if (existingIndex >= 0) {
          const updated = [...prev];
          updated[existingIndex] = data;
          return updated;
        } else {
          return [...prev, data];
        }
      });
    });

    newSocket.on('unit_update', (data: ResponseUnit) => {
      setResponseUnits(prev => {
        const existingIndex = prev.findIndex(unit => unit.id === data.id);
        if (existingIndex >= 0) {
          const updated = [...prev];
          updated[existingIndex] = data;
          return updated;
        } else {
          return [...prev, data];
        }
      });
    });

    newSocket.on('video_feed_update', (data: VideoFeed) => {
      setVideoFeeds(prev => {
        const existingIndex = prev.findIndex(feed => feed.id === data.id);
        if (existingIndex >= 0) {
          const updated = [...prev];
          updated[existingIndex] = data;
          return updated;
        } else {
          return [...prev, data];
        }
      });
    });

    newSocket.on('system_alert', (data: SystemAlert) => {
      setAlerts(prev => [data, ...prev.slice(0, 49)]); // Keep last 50 alerts
    });

    setSocket(newSocket);

    // Cleanup on unmount
    return () => {
      newSocket.close();
    };
  }, []);

  const sendMessage = (message: any) => {
    if (socket && connected) {
      socket.emit('message', message);
    }
  };

  const acknowledgeAlert = (alertId: string) => {
    setAlerts(prev => 
      prev.map(alert => 
        alert.id === alertId 
          ? { ...alert, acknowledged: true, acknowledgedBy: 'current_user' }
          : alert
      )
    );
    
    if (socket && connected) {
      socket.emit('acknowledge_alert', { alertId });
    }
  };

  const value: SocketContextType = {
    socket,
    connected,
    incidents,
    responseUnits,
    videoFeeds,
    alerts,
    sendMessage,
    acknowledgeAlert,
  };

  return (
    <SocketContext.Provider value={value}>
      {children}
    </SocketContext.Provider>
  );
};

export const useSocket = (): SocketContextType => {
  const context = useContext(SocketContext);
  if (context === undefined) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  return context;
};
