import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
  Alert,
  LinearProgress,
} from '@mui/material';
import {
  Warning as WarningIcon,
  LocalFireDepartment as FireIcon,
  LocalHospital as MedicalIcon,
  Security as SecurityIcon,
  MoreVert as MoreIcon,
  CheckCircle as CheckIcon,
  Schedule as ScheduleIcon,
  Person as PersonIcon,
} from '@mui/icons-material';
import { useSocket } from '../../contexts/SocketContext';
import { Incident } from '../../types';

interface IncidentCardProps {
  incident: Incident;
  compact?: boolean;
  onSelect: (incident: Incident) => void;
}

const IncidentCard: React.FC<IncidentCardProps> = ({ incident, compact = false, onSelect }) => {
  const getIncidentIcon = (type: string) => {
    switch (type) {
      case 'fire': return <FireIcon />;
      case 'medical': return <MedicalIcon />;
      case 'security': return <SecurityIcon />;
      default: return <WarningIcon />;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'error';
      case 'high': return 'warning';
      case 'medium': return 'info';
      case 'low': return 'success';
      default: return 'default';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'detected': return 'warning';
      case 'verified': return 'info';
      case 'responding': return 'primary';
      case 'resolved': return 'success';
      default: return 'default';
    }
  };

  const timeAgo = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours}h ago`;
    return date.toLocaleDateString();
  };

  return (
    <Paper
      sx={{
        p: compact ? 1 : 2,
        mb: 1,
        cursor: 'pointer',
        border: incident.severity === 'critical' ? '2px solid #f44336' : 'none',
        '&:hover': {
          backgroundColor: 'rgba(255, 255, 255, 0.05)',
        },
      }}
      onClick={() => onSelect(incident)}
    >
      <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
        <Box sx={{ color: getSeverityColor(incident.severity) + '.main', mt: 0.5 }}>
          {getIncidentIcon(incident.type)}
        </Box>
        
        <Box sx={{ flexGrow: 1, minWidth: 0 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
            <Typography variant={compact ? 'body2' : 'subtitle1'} sx={{ fontWeight: 'bold' }}>
              {incident.type.replace('_', ' ').toUpperCase()}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {timeAgo(incident.timestamp)}
            </Typography>
          </Box>
          
          <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
            {incident.location.zone} - {incident.location.description}
          </Typography>
          
          {!compact && (
            <Typography variant="body2" sx={{ mb: 1 }}>
              {incident.description}
            </Typography>
          )}
          
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', alignItems: 'center' }}>
            <Chip
              label={incident.severity}
              color={getSeverityColor(incident.severity) as any}
              size="small"
            />
            <Chip
              label={incident.status}
              color={getStatusColor(incident.status) as any}
              size="small"
              variant="outlined"
            />
            {incident.confidence && (
              <Chip
                label={`${Math.round(incident.confidence * 100)}% confidence`}
                size="small"
                variant="outlined"
              />
            )}
          </Box>
          
          {incident.assignedUnits.length > 0 && (
            <Box sx={{ mt: 1, display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <PersonIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
              <Typography variant="caption" color="text.secondary">
                {incident.assignedUnits.length} unit(s) assigned
              </Typography>
            </Box>
          )}
        </Box>
      </Box>
    </Paper>
  );
};

interface IncidentPanelProps {
  compact?: boolean;
}

const IncidentPanel: React.FC<IncidentPanelProps> = ({ compact = false }) => {
  const { incidents } = useSocket();
  const [selectedIncident, setSelectedIncident] = useState<Incident | null>(null);
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterSeverity, setFilterSeverity] = useState<string>('all');

  // Mock incidents if none exist
  const mockIncidents: Incident[] = [
    {
      id: '1',
      type: 'crowd_surge',
      severity: 'critical',
      status: 'responding',
      location: {
        lat: 40.7128,
        lng: -74.0060,
        zone: 'Main Stage',
        description: 'Front barrier area',
      },
      timestamp: new Date(Date.now() - 300000),
      description: 'Crowd density exceeding safe limits detected near main stage',
      confidence: 0.92,
      videoFeedId: '1',
      assignedUnits: ['unit-1', 'unit-2'],
      aiAnalysis: {
        summary: 'High crowd density detected with potential for crowd surge',
        keyFrames: [],
        detectionDetails: {},
      },
    },
    {
      id: '2',
      type: 'medical',
      severity: 'high',
      status: 'verified',
      location: {
        lat: 40.7130,
        lng: -74.0058,
        zone: 'Food Court',
        description: 'Near vendor booth 12',
      },
      timestamp: new Date(Date.now() - 600000),
      description: 'Person collapsed, medical attention required',
      confidence: 0.87,
      videoFeedId: '3',
      assignedUnits: ['medical-1'],
      humanVerification: {
        verified: true,
        verifiedBy: 'operator-1',
        verificationTime: new Date(Date.now() - 580000),
        notes: 'Confirmed medical emergency, ambulance dispatched',
      },
    },
    {
      id: '3',
      type: 'fire',
      severity: 'medium',
      status: 'resolved',
      location: {
        lat: 40.7125,
        lng: -74.0065,
        zone: 'Backstage',
        description: 'Equipment area',
      },
      timestamp: new Date(Date.now() - 1800000),
      description: 'Smoke detected from electrical equipment',
      confidence: 0.78,
      videoFeedId: '4',
      assignedUnits: ['fire-1'],
    },
  ];

  const allIncidents = incidents.length > 0 ? incidents : mockIncidents;
  
  const filteredIncidents = allIncidents.filter(incident => {
    if (filterStatus !== 'all' && incident.status !== filterStatus) return false;
    if (filterSeverity !== 'all' && incident.severity !== filterSeverity) return false;
    return true;
  });

  const displayIncidents = compact ? filteredIncidents.slice(0, 5) : filteredIncidents;

  const handleIncidentSelect = (incident: Incident) => {
    setSelectedIncident(incident);
  };

  const closeIncidentDialog = () => {
    setSelectedIncident(null);
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">
          Incidents {compact && `(${displayIncidents.length})`}
        </Typography>
        
        {!compact && (
          <Box sx={{ display: 'flex', gap: 1 }}>
            <FormControl size="small" sx={{ minWidth: 100 }}>
              <InputLabel>Status</InputLabel>
              <Select
                value={filterStatus}
                label="Status"
                onChange={(e) => setFilterStatus(e.target.value)}
              >
                <MenuItem value="all">All</MenuItem>
                <MenuItem value="detected">Detected</MenuItem>
                <MenuItem value="verified">Verified</MenuItem>
                <MenuItem value="responding">Responding</MenuItem>
                <MenuItem value="resolved">Resolved</MenuItem>
              </Select>
            </FormControl>
            
            <FormControl size="small" sx={{ minWidth: 100 }}>
              <InputLabel>Severity</InputLabel>
              <Select
                value={filterSeverity}
                label="Severity"
                onChange={(e) => setFilterSeverity(e.target.value)}
              >
                <MenuItem value="all">All</MenuItem>
                <MenuItem value="critical">Critical</MenuItem>
                <MenuItem value="high">High</MenuItem>
                <MenuItem value="medium">Medium</MenuItem>
                <MenuItem value="low">Low</MenuItem>
              </Select>
            </FormControl>
          </Box>
        )}
      </Box>

      {/* Incidents List */}
      <Box sx={{ maxHeight: compact ? 400 : 'none', overflow: 'auto' }}>
        {displayIncidents.length === 0 ? (
          <Paper sx={{ p: 3, textAlign: 'center' }}>
            <CheckIcon sx={{ fontSize: 48, color: 'success.main', mb: 1 }} />
            <Typography variant="h6" color="success.main">
              No Active Incidents
            </Typography>
            <Typography variant="body2" color="text.secondary">
              All systems operating normally
            </Typography>
          </Paper>
        ) : (
          displayIncidents.map((incident) => (
            <IncidentCard
              key={incident.id}
              incident={incident}
              compact={compact}
              onSelect={handleIncidentSelect}
            />
          ))
        )}
      </Box>

      {/* Incident Detail Dialog */}
      <Dialog
        open={!!selectedIncident}
        onClose={closeIncidentDialog}
        maxWidth="md"
        fullWidth
      >
        {selectedIncident && (
          <>
            <DialogTitle>
              Incident Details - {selectedIncident.type.replace('_', ' ').toUpperCase()}
            </DialogTitle>
            <DialogContent>
              <Box sx={{ mb: 2 }}>
                <Typography variant="body1" gutterBottom>
                  <strong>Location:</strong> {selectedIncident.location.zone} - {selectedIncident.location.description}
                </Typography>
                <Typography variant="body1" gutterBottom>
                  <strong>Time:</strong> {selectedIncident.timestamp.toLocaleString()}
                </Typography>
                <Typography variant="body1" gutterBottom>
                  <strong>Description:</strong> {selectedIncident.description}
                </Typography>
              </Box>
              
              <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                <Chip label={selectedIncident.severity} color="error" />
                <Chip label={selectedIncident.status} color="primary" />
                <Chip label={`${Math.round(selectedIncident.confidence * 100)}% confidence`} />
              </Box>
              
              {selectedIncident.aiAnalysis && (
                <Alert severity="info" sx={{ mb: 2 }}>
                  <strong>AI Analysis:</strong> {selectedIncident.aiAnalysis.summary}
                </Alert>
              )}
              
              {selectedIncident.humanVerification && (
                <Alert severity="success" sx={{ mb: 2 }}>
                  <strong>Human Verification:</strong> Verified by {selectedIncident.humanVerification.verifiedBy} at {selectedIncident.humanVerification.verificationTime.toLocaleString()}
                  {selectedIncident.humanVerification.notes && (
                    <Typography variant="body2" sx={{ mt: 1 }}>
                      Notes: {selectedIncident.humanVerification.notes}
                    </Typography>
                  )}
                </Alert>
              )}
            </DialogContent>
            <DialogActions>
              <Button onClick={closeIncidentDialog}>Close</Button>
              <Button variant="contained" color="primary">
                Take Action
              </Button>
            </DialogActions>
          </>
        )}
      </Dialog>
    </Box>
  );
};

export default IncidentPanel;
